# Use AWS Lambda Python base image for x86_64 architecture
FROM public.ecr.aws/lambda/python:3.9

# Set working directory
WORKDIR ${LAMBDA_TASK_ROOT}

# Copy requirements first for better caching
COPY requirements.txt .

# Install system dependencies for lxml and clean up
RUN yum install -y libxml2-devel libxslt-devel gcc && \
    pip install --no-cache-dir -r requirements.txt && \
    yum remove -y gcc && \
    yum clean all && \
    rm -rf /var/cache/yum

# Copy only necessary application files
COPY change_detector.py .
COPY content_extractor.py .
COPY embedding_generator.py .
COPY knowledge_pipeline.py .
COPY s3_manager.py .
COPY msal_auth_manager.py .

# Note: MSAL cache files are now downloaded from S3 at runtime
# No need to copy static cache files

# Set environment variables
ENV PYTHONPATH=${LAMBDA_TASK_ROOT}
ENV PYTHONUNBUFFERED=1
ENV FORCE_REBUILD=1

# Use the correct Lambda entrypoint format
CMD ["knowledge_pipeline.lambda_handler"] 